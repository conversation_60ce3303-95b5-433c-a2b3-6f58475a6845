from datetime import datetime
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, EmailStr

from app.core.database import get_database
from app.models.user import User, UserCreate, UserUpdate
from app.services.user_service import UserService

router = APIRouter()


class RegisterRequest(BaseModel):
    uuid: Optional[str] = None
    email: Optional[EmailStr] = None
    nickname: Optional[str] = None
    goal: Optional[int] = 2000
    cup_size: Optional[int] = 250


class LoginRequest(BaseModel):
    email: EmailStr
    password: str


@router.post("/register")
async def register_user(request: RegisterRequest, db=Depends(get_database)):
    """Register a new user or bind anonymous user (支持用指定uuid新建)"""
    try:
        user_service = UserService(db)
        if request.uuid:
            # 用指定uuid查找或新建
            user = await user_service.create_or_get_user_with_uuid(
                uuid=request.uuid,
                goal=request.goal,
                cup_size=request.cup_size,
                nickname=request.nickname,
                email=request.email
            )
        else:
            # Create new anonymous user
            user = await user_service.create_anonymous_user(
                goal=request.goal,
                cup_size=request.cup_size
            )
        return {"success": True, "user": user}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/login")
async def login_user(request: LoginRequest, db=Depends(get_database)):
    """Login user with email and password"""
    try:
        user_service = UserService(db)
        user = await user_service.authenticate_user(request.email, request.password)
        if not user:
            raise HTTPException(status_code=401, detail="Invalid credentials")
        
        # Generate access token here if needed
        return {"success": True, "user": user}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/{user_id}")
async def get_user(user_id: str, db=Depends(get_database)):
    """Get user profile"""
    try:
        user_service = UserService(db)
        user = await user_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        return user
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{user_id}")
async def update_user(user_id: str, update_data: UserUpdate, db=Depends(get_database)):
    """Update user profile"""
    try:
        user_service = UserService(db)
        user = await user_service.update_user(user_id, update_data)
        return {"success": True, "user": user}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/{user_id}/stats")
async def get_user_stats(user_id: str, db=Depends(get_database)):
    """Get user statistics"""
    try:
        user_service = UserService(db)
        stats = await user_service.get_user_stats(user_id)
        return stats
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

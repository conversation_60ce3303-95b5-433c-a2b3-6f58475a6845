import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from motor.motor_asyncio import AsyncIOMotorDatabase
from bson import ObjectId
from passlib.context import CryptContext

from app.models.user import User, UserCreate, UserUpdate, UserInDB
from app.core.config import settings


class UserService:
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db
        self.users = db.users
        self.drink_logs = db.drink_logs
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return self.pwd_context.verify(plain_password, hashed_password)

    def get_password_hash(self, password: str) -> str:
        """Hash a password"""
        return self.pwd_context.hash(password)

    async def create_anonymous_user(self, goal: int = None, cup_size: int = None) -> Dict[str, Any]:
        """Create a new anonymous user"""
        user_uuid = str(uuid.uuid4())
        
        user_data = {
            "uuid": user_uuid,
            "nickname": "Quacky",
            "goal": goal or settings.DEFAULT_DAILY_GOAL,
            "cup_size": cup_size or settings.DEFAULT_CUP_SIZE,
            "friends": [],
            "achievements": [],
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }
        
        result = await self.users.insert_one(user_data)
        user_data["_id"] = result.inserted_id
        
        return {
            "id": str(result.inserted_id),
            "uuid": user_uuid,
            "nickname": user_data["nickname"],
            "goal": user_data["goal"],
            "cup_size": user_data["cup_size"]
        }

    async def bind_anonymous_user(self, uuid: str, email: str = None, nickname: str = None) -> Dict[str, Any]:
        """Bind an anonymous user with email/nickname"""
        user = await self.users.find_one({"uuid": uuid})
        if not user:
            raise ValueError("User not found")

        update_data = {"updated_at": datetime.now()}
        if email:
            # Check if email already exists
            existing_user = await self.users.find_one({"email": email})
            if existing_user and str(existing_user["_id"]) != str(user["_id"]):
                raise ValueError("Email already registered")
            update_data["email"] = email
        
        if nickname:
            update_data["nickname"] = nickname

        await self.users.update_one(
            {"_id": user["_id"]},
            {"$set": update_data}
        )

        # Return updated user
        updated_user = await self.users.find_one({"_id": user["_id"]})
        return {
            "id": str(updated_user["_id"]),
            "uuid": updated_user["uuid"],
            "email": updated_user.get("email"),
            "nickname": updated_user["nickname"],
            "goal": updated_user["goal"],
            "cup_size": updated_user["cup_size"]
        }

    async def authenticate_user(self, email: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user with email and password"""
        user = await self.users.find_one({"email": email})
        if not user:
            return None
        
        if not user.get("hashed_password"):
            return None
            
        if not self.verify_password(password, user["hashed_password"]):
            return None
            
        return {
            "id": str(user["_id"]),
            "uuid": user["uuid"],
            "email": user["email"],
            "nickname": user["nickname"],
            "goal": user["goal"],
            "cup_size": user["cup_size"]
        }

    async def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user by ID"""
        if isinstance(user_id, str):
            user_object_id = ObjectId(user_id)
        else:
            user_object_id = user_id

        user = await self.users.find_one({"_id": user_object_id})
        if not user:
            return None

        return {
            "id": str(user["_id"]),
            "uuid": user["uuid"],
            "email": user.get("email"),
            "nickname": user["nickname"],
            "goal": user["goal"],
            "cup_size": user["cup_size"],
            "avatar": user.get("avatar"),
            "friends": user.get("friends", []),
            "achievements": user.get("achievements", []),
            "created_at": user["created_at"],
            "updated_at": user["updated_at"]
        }

    async def update_user(self, user_id: str, update_data: UserUpdate) -> Dict[str, Any]:
        """Update user profile"""
        if isinstance(user_id, str):
            user_object_id = ObjectId(user_id)
        else:
            user_object_id = user_id

        # Prepare update data
        update_dict = {}
        if update_data.email is not None:
            # Check if email already exists
            existing_user = await self.users.find_one({"email": update_data.email})
            if existing_user and str(existing_user["_id"]) != str(user_object_id):
                raise ValueError("Email already registered")
            update_dict["email"] = update_data.email
        
        if update_data.nickname is not None:
            update_dict["nickname"] = update_data.nickname
        if update_data.goal is not None:
            update_dict["goal"] = update_data.goal
        if update_data.cup_size is not None:
            update_dict["cup_size"] = update_data.cup_size
        if update_data.avatar is not None:
            update_dict["avatar"] = update_data.avatar

        update_dict["updated_at"] = datetime.now()

        await self.users.update_one(
            {"_id": user_object_id},
            {"$set": update_dict}
        )

        # Return updated user
        return await self.get_user_by_id(str(user_object_id))

    async def get_user_stats(self, user_id: str) -> Dict[str, Any]:
        """Get user statistics"""
        if isinstance(user_id, str):
            user_object_id = ObjectId(user_id)
        else:
            user_object_id = user_id

        # Get user
        user = await self.get_user_by_id(str(user_object_id))
        if not user:
            raise ValueError("User not found")

        # Calculate stats from drink logs
        total_logs = await self.drink_logs.count_documents({"user_id": user_object_id})
        
        # Get total water consumed
        pipeline = [
            {"$match": {"user_id": user_object_id}},
            {"$group": {"_id": None, "total_water": {"$sum": "$total_today"}}}
        ]
        total_result = await self.drink_logs.aggregate(pipeline).to_list(length=1)
        total_water = total_result[0]["total_water"] if total_result else 0

        # Calculate current streak
        current_streak = await self._calculate_current_streak(user_object_id, user["goal"])

        return {
            "total_days_logged": total_logs,
            "total_water_ml": total_water,
            "total_water_liters": round(total_water / 1000, 2),
            "current_streak": current_streak,
            "achievements_count": len(user.get("achievements", [])),
            "friends_count": len(user.get("friends", []))
        }

    async def _calculate_current_streak(self, user_id: ObjectId, goal: int) -> int:
        """Calculate current consecutive days streak"""
        from datetime import date, timedelta
        
        current_date = date.today()
        streak = 0
        
        # Check each day going backwards
        for i in range(365):  # Max 365 days to check
            check_date = current_date - timedelta(days=i)
            date_str = check_date.isoformat()
            
            daily_log = await self.drink_logs.find_one({
                "user_id": user_id,
                "date": date_str
            })
            
            if daily_log and daily_log["total_today"] >= goal:
                streak += 1
            else:
                break
                
        return streak

    async def create_or_get_user_with_uuid(self, uuid: str, goal: int = None, cup_size: int = None, nickname: str = None, email: str = None, avatar: str = None) -> Dict[str, Any]:
        """用指定uuid查找或新建用户"""
        user = await self.users.find_one({"uuid": uuid})
        if user:
            return {
                "id": str(user["_id"]),
                "uuid": user["uuid"],
                "nickname": user.get("nickname", "Quacky"),
                "email": user.get("email"),
                "avatar": user.get("avatar"),
                "goal": user.get("goal", goal or settings.DEFAULT_DAILY_GOAL),
                "cup_size": user.get("cup_size", cup_size or settings.DEFAULT_CUP_SIZE),
            }
        # 新建用户
        user_data = {
            "uuid": uuid,
            "nickname": nickname or "Quacky",
            "email": email,
            "avatar": avatar,
            "goal": goal or settings.DEFAULT_DAILY_GOAL,
            "cup_size": cup_size or settings.DEFAULT_CUP_SIZE,
            "friends": [],
            "achievements": [],
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }
        result = await self.users.insert_one(user_data)
        user_data["_id"] = result.inserted_id
        return {
            "id": str(result.inserted_id),
            "uuid": uuid,
            "nickname": user_data["nickname"],
            "email": user_data["email"],
            "avatar": user_data["avatar"],
            "goal": user_data["goal"],
            "cup_size": user_data["cup_size"]
        }
